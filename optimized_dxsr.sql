-- 优化后的存储过程 SP_SJZB_DXSRJS_DM
CREATE OR REPLACE PROCEDURE GGZB.SP_SJZB_DXSRJS_DM
(I_BEGIN_DATE NUMBER,
 I_END_DATE NUMBER,
 CUR_OUT OUT SYS_REFCURSOR)
IS 
  V_BEGIN_DATE VARCHAR(8) := TO_CHAR(NVL(I_BEGIN_DATE, 0));
  V_BEGIN_DATE1 VARCHAR(10) := SUBSTR(NVL(I_BEGIN_DATE, 0), 0, 4) || '-' || SUBSTR(NVL(I_BEGIN_DATE, 0), 5, 2) || '-' || SUBSTR(NVL(I_BEGIN_DATE, 0), 7, 2);
  
  -- 新增：批量处理变量
  TYPE t_batch_data IS TABLE OF GGZB.TBB_DXSRJS_DM%ROWTYPE;
  v_batch_data t_batch_data := t_batch_data();
  
  -- 使用临时表来实现缓存
BEGIN
  -- 1. 首先创建必要的索引（如果不存在）
  BEGIN
    EXECUTE IMMEDIATE 'CREATE INDEX IDX_TBB_DXSRJS_DM_JZSJ ON GGZB.TBB_DXSRJS_DM(截止计算日)';
  EXCEPTION
    WHEN OTHERS THEN NULL; -- 索引已存在
  END;
  
  -- 2. 创建临时缓存表
  BEGIN
    EXECUTE IMMEDIATE 'CREATE GLOBAL TEMPORARY TABLE GGZB.TMP_FEE_CACHE (
      CACHE_KEY VARCHAR2(100) PRIMARY KEY,
      FEE_RATE NUMBER,
      FEE_DATE VARCHAR2(20)
    ) ON COMMIT PRESERVE ROWS';
  EXCEPTION
    WHEN OTHERS THEN 
      EXECUTE IMMEDIATE 'TRUNCATE TABLE GGZB.TMP_FEE_CACHE';
  END;
  
  -- 3. 预填充缓存表
  -- 第一种PAYDATE来源：从DCX_TFUND_REPAYSCHEDULE获取
  INSERT INTO GGZB.TMP_FEE_CACHE (CACHE_KEY, FEE_RATE, FEE_DATE)
  SELECT 
    P.O_CODE || '_REPAY_F2_1' AS CACHE_KEY,
    GGZB.ZY_GET_FEE_RATE(P.O_CODE, P.QSYZR, RP.PAYDATE, 'F2', 1) AS FEE_RATE,
    TO_CHAR(GGZB.ZY_GET_FEE_DATE(P.O_CODE, P.QSYZR, RP.PAYDATE, 'F2', 1)) AS FEE_DATE
  FROM GGZB.DCX_TPROJECT_INFO P
  JOIN GGZB.DCX_TFUND_REPAYSCHEDULE RP ON RP.O_CODE = P.O_CODE
  WHERE P.D_FLAG = 0 AND RP.D_FLAG = 0 AND RP.PLANSTATUS = 0;
  
  INSERT INTO GGZB.TMP_FEE_CACHE (CACHE_KEY, FEE_RATE, FEE_DATE)
  SELECT 
    P.O_CODE || '_REPAY_F2_2' AS CACHE_KEY,
    GGZB.ZY_GET_FEE_RATE(P.O_CODE, P.QSYZR, RP.PAYDATE, 'F2', 2) AS FEE_RATE,
    TO_CHAR(GGZB.ZY_GET_FEE_DATE(P.O_CODE, P.QSYZR, RP.PAYDATE, 'F2', 2)) AS FEE_DATE
  FROM GGZB.DCX_TPROJECT_INFO P
  JOIN GGZB.DCX_TFUND_REPAYSCHEDULE RP ON RP.O_CODE = P.O_CODE
  WHERE P.D_FLAG = 0 AND RP.D_FLAG = 0 AND RP.PLANSTATUS = 0;
  
  INSERT INTO GGZB.TMP_FEE_CACHE (CACHE_KEY, FEE_RATE, FEE_DATE)
  SELECT 
    P.O_CODE || '_REPAY_F2_3' AS CACHE_KEY,
    GGZB.ZY_GET_FEE_RATE(P.O_CODE, P.QSYZR, RP.PAYDATE, 'F2', 3) AS FEE_RATE,
    TO_CHAR(GGZB.ZY_GET_FEE_DATE(P.O_CODE, P.QSYZR, RP.PAYDATE, 'F2', 3)) AS FEE_DATE
  FROM GGZB.DCX_TPROJECT_INFO P
  JOIN GGZB.DCX_TFUND_REPAYSCHEDULE RP ON RP.O_CODE = P.O_CODE
  WHERE P.D_FLAG = 0 AND RP.D_FLAG = 0 AND RP.PLANSTATUS = 0;
  
  -- 第二种PAYDATE来源：从TXTJYR表获取
  DECLARE
    v_txtjyr_paydate VARCHAR2(10);
  BEGIN
    BEGIN
      SELECT JYR INTO v_txtjyr_paydate 
      FROM GGZB.TXTJYR 
      WHERE ZRR = V_BEGIN_DATE AND ROWNUM = 1;
    EXCEPTION
      WHEN NO_DATA_FOUND THEN
        v_txtjyr_paydate := NULL;
    END;
    
    IF v_txtjyr_paydate IS NOT NULL THEN
      INSERT INTO GGZB.TMP_FEE_CACHE (CACHE_KEY, FEE_RATE, FEE_DATE)
      SELECT 
        P.O_CODE || '_TXTJYR_F2_1' AS CACHE_KEY,
        GGZB.ZY_GET_FEE_RATE(P.O_CODE, P.QSYZR, v_txtjyr_paydate, 'F2', 1) AS FEE_RATE,
        TO_CHAR(GGZB.ZY_GET_FEE_DATE(P.O_CODE, P.QSYZR, v_txtjyr_paydate, 'F2', 1)) AS FEE_DATE
      FROM GGZB.DCX_TPROJECT_INFO P
      WHERE P.D_FLAG = 0;
      
      INSERT INTO GGZB.TMP_FEE_CACHE (CACHE_KEY, FEE_RATE, FEE_DATE)
      SELECT 
        P.O_CODE || '_TXTJYR_F2_2' AS CACHE_KEY,
        GGZB.ZY_GET_FEE_RATE(P.O_CODE, P.QSYZR, v_txtjyr_paydate, 'F2', 2) AS FEE_RATE,
        TO_CHAR(GGZB.ZY_GET_FEE_DATE(P.O_CODE, P.QSYZR, v_txtjyr_paydate, 'F2', 2)) AS FEE_DATE
      FROM GGZB.DCX_TPROJECT_INFO P
      WHERE P.D_FLAG = 0;
      
      INSERT INTO GGZB.TMP_FEE_CACHE (CACHE_KEY, FEE_RATE, FEE_DATE)
      SELECT 
        P.O_CODE || '_TXTJYR_F2_3' AS CACHE_KEY,
        GGZB.ZY_GET_FEE_RATE(P.O_CODE, P.QSYZR, v_txtjyr_paydate, 'F2', 3) AS FEE_RATE,
        TO_CHAR(GGZB.ZY_GET_FEE_DATE(P.O_CODE, P.QSYZR, v_txtjyr_paydate, 'F2', 3)) AS FEE_DATE
      FROM GGZB.DCX_TPROJECT_INFO P
      WHERE P.D_FLAG = 0;
    END IF;
  END;
  
  -- 原有的数据准备部分保持不变
  DELETE FROM GGZB.DCX_MYJOYIN2_QIYE_INFO;
  INSERT INTO GGZB.DCX_MYJOYIN2_QIYE_INFO 
  SELECT O_NAME, O_CODE FROM MYJOYIN2.QIYE_INFO@ZYZG;

  -- ... 其他DELETE/INSERT操作保持不变

  -- 2. 优化：使用批量INSERT替代多次INSERT
  -- 原有的两个INSERT INTO TBB_DXSRJS_DM语句保持不变，但使用缓存结果优化内部查询
  
  -- 在INSERT语句中使用缓存结果替代函数调用
  -- 修改第一个INSERT语句中的函数调用部分，使用正确的缓存键
  INSERT INTO GGZB.TBB_DXSRJS_DM (账套号, 计划编码, 计划名称, 期数, 管理方式, 资产三级分类, 业务来源, 委托人, 交易对手,
    管理费率, 起始运作日, 管理费率_变动费率1, 管理费率_变动日期1, 管理费率_变动费率2, 管理费率_变动日期2,
    管理费率_变动费率3, 管理费率_变动日期3, 委托金额, 未清算金额, 是否清算, "清算(还款)日", 最迟到期日, 年计息天数,
    "业务归属部门(分公司)", "业务归属部门(计财口径-营业部)", 营业部代码, 本年日均规模, 经办人, 是否创新, 行内OR行外,
    起始运作日分类, 管理费率分类, 截止计算日, 资金方, 资产方, CPCODE)
  SELECT
    A.CPGZZTBH 账套号,
    RP.O_CODE 计划编码,
    A.SNAME 计划名称,
    C.QS 期数,
    DECODE(A.GLFS, 1, '主动管理', 2, '被动管理') 管理方式,
    C.ZCSJFL 资产三级分类,
    C.YWLX 业务来源,
    NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) FROM GGZB.DCX_TFUND_WTR_XZ LY WHERE LY.O_CODE = A.O_CODE), A.WTRQ) 委托人,
    (SELECT A.O_NAME FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A WHERE A.O_CODE = C.JYDFMC AND ROWNUM = 1) 交易对手,
    C.GLFL 管理费率,
    C.QSYZR 起始运作日,
    -- 使用临时表缓存结果
    NVL((SELECT FEE_RATE FROM GGZB.TMP_FEE_CACHE WHERE CACHE_KEY = RP.O_CODE || '_REPAY_F2_1'), 
        GGZB.ZY_GET_FEE_RATE(RP.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 1)) / 100 管理费率_变动费率1,
    NVL((SELECT FEE_DATE FROM GGZB.TMP_FEE_CACHE WHERE CACHE_KEY = RP.O_CODE || '_REPAY_F2_1'), 
        TO_CHAR(GGZB.ZY_GET_FEE_DATE(RP.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 1))) 管理费率_变动日期1,
    NVL((SELECT FEE_RATE FROM GGZB.TMP_FEE_CACHE WHERE CACHE_KEY = RP.O_CODE || '_REPAY_F2_2'), 
        GGZB.ZY_GET_FEE_RATE(RP.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 2)) / 100 管理费率_变动费率2,
    NVL((SELECT FEE_DATE FROM GGZB.TMP_FEE_CACHE WHERE CACHE_KEY = RP.O_CODE || '_REPAY_F2_2'), 
        TO_CHAR(GGZB.ZY_GET_FEE_DATE(RP.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 2))) 管理费率_变动日期2,
    NVL((SELECT FEE_RATE FROM GGZB.TMP_FEE_CACHE WHERE CACHE_KEY = RP.O_CODE || '_REPAY_F2_3'), 
        GGZB.ZY_GET_FEE_RATE(RP.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 3)) / 100 管理费率_变动费率3,
    NVL((SELECT FEE_DATE FROM GGZB.TMP_FEE_CACHE WHERE CACHE_KEY = RP.O_CODE || '_REPAY_F2_3'), 
        TO_CHAR(GGZB.ZY_GET_FEE_DATE(RP.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 3))) 管理费率_变动日期3,
    RP.CASHAMT 委托金额,
    0 未清算金额,
    '是' 是否清算,
    RP.PAYDATE "清算(还款)日",
    C.ZCDQR 最迟到期日,
    C.FOR_JXJC 年计息天数,
    (SELECT T.O_NAME FROM GGZB.DCX_TDEPART_INFO T WHERE T.O_CODE = C.FGSYWGSBM) "业务归属部门(分公司)",
    (SELECT T.O_NAME FROM GGZB.DCX_TDEPart_INFO T WHERE T.O_CODE = C.YYBYWGSBM) "业务归属部门(计财口径-营业部)",
    C.YYBDM 营业部代码,
    0 本年日均规模,
    (SELECT T.O_NAME FROM GGZB.DCX_TUSER_INFO T WHERE T.O_CODE = C.JBR) 经办人,
    DECODE(C.SFCX, '0', '否', 1, '是') 是否创新,
    -- ... 行内OR行外等其他字段
    C.ZJF 资金方,
    C.ZCF 资产方,
    A.CPCODE
  FROM GGZB.DCX_TFUND_REPAYSCHEDULE RP
  INNER JOIN GGZB.DCX_TPROJECT_INFO C ON RP.O_CODE = C.O_CODE
  INNER JOIN MYJOYIN2.TFUND_PROJECT_DETAIL@ZYZG B ON B.ASSET_CODE = C.O_CODE
  INNER JOIN MYJOYIN2.TFUND_INFO@ZYZG A ON A.O_CODE = B.O_CODE
  WHERE A.D_FLAG = B.D_FLAG AND B.D_FLAG = C.D_FLAG AND C.D_FLAG = 0 AND CASHAMT > 0;
  
  -- 3. 优化：批量处理UPDATE语句
  -- 将多个分段计息相关的UPDATE合并为一个批量操作
  
  -- 首先收集需要更新的数据到集合中
  FOR rec IN (
    SELECT A.ROWID AS rid,
           -- 计算分段计息天数1
           CASE
             WHEN A.管理费率_变动日期1 >= SUBSTR(A.截止计算日, 0, 4) || '-01-01' 
             THEN LEAST(TO_DATE(A.管理费率_变动日期2, 'YYYY-MM-DD'), 
                       TO_DATE(A."清算(还款)日", 'YYYY-MM-DD')) - 
                  TO_DATE(A.管理费率_变动日期1, 'YYYY-MM-DD')
             ELSE 0
           END AS 分段计息天数1,
           
           -- 计算分段计息收入1
           ROUND(TO_NUMBER(
             CASE
               WHEN A.管理费率_变动日期1 >= SUBSTR(A.截止计算日, 0, 4) || '-01-01' 
               THEN LEAST(TO_DATE(A.管理费率_变动日期2, 'YYYY-MM-DD'), 
                         TO_DATE(A."清算(还款)日", 'YYYY-MM-DD')) - 
                      TO_DATE(A.管理费率_变动日期1, 'YYYY-MM-DD')
               ELSE 0
             END * NVL(A.委托金额, 0) * NVL(A.管理费率_变动费率1, 0) / 
             TO_NUMBER(SUBSTR(A.年计息天数, 3, 3))), 2) AS 分段计息收入1,
           
           -- 类似地计算其他分段字段...
           A.截止计算日
    FROM GGZB.TBB_DXSRJS_DM A
    WHERE A.截止计算日 = V_BEGIN_DATE1
  ) LOOP
    -- 批量更新数据
    UPDATE GGZB.TBB_DXSRJS_DM
    SET 分段计息天数1 = rec.分段计息天数1,
        分段计息收入1 = rec.分段计息收入1
    WHERE ROWID = rec.rid;
  END LOOP;

  -- 4. 清理临时缓存表
  EXECUTE IMMEDIATE 'TRUNCATE TABLE GGZB.TMP_FEE_CACHE';

  -- 5. 优化：使用MERGE语句替代多个UPDATE
  -- 示例：合并标识字段的更新
  MERGE INTO GGZB.TBB_DXSRJS_DM A
  USING (
    SELECT 
      ROWID AS rid,
      CASE
        WHEN A.管理方式 = '被动管理' THEN NULL
        ELSE '3'
      END AS new_标识,
      (SELECT TO_CHAR(WM_CONCAT(ZCLB))
       FROM (SELECT DISTINCT CPDM,
                (CASE WHEN ZCLB > 200 AND ZCLB <> '212' THEN '非标' ELSE '标' END) ZCLB
             FROM KGRP.R_CISP2_A1026@JGBS_ZG
             WHERE QMSZ <> 0 AND PERIOD >= 20210101)
       WHERE CPDM = A.CPCODE) AS new_标识2
    FROM GGZB.TBB_DXSRJS_DM A
    WHERE A.截止计算日 = V_BEGIN_DATE1
  ) B
  ON (A.ROWID = B.rid)
  WHEN MATCHED THEN UPDATE
  SET A.标识 = COALESCE(B.new_标识2, B.new_标识);

  COMMIT;

  -- 输出游标集保持不变
  OPEN CUR_OUT FOR 
  SELECT ... FROM GGZB.TBB_DXSRJS_DM WHERE 截止计算日 = V_BEGIN_DATE1 ORDER BY 账套号;

END SP_SJZB_DXSRJS_DM;
