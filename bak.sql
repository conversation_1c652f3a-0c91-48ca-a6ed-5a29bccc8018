CREATE OR REPLACE PROCEDURE GGZB.SP_SJZB_DXSRJS_DM --定向收入计算

(I_BEGIN_DATE NUMBER, --开始日期
 I_END_DATE   NUMBER, --结束日期
 CUR_OUT      OUT SYS_REFCURSOR)
--============================================================================
  --对象编号   :
  --对象名称   : 定向收入计算
  --对象   : SP_SJZB_DXSRJS_DM
  --创 建 人   : baoanyi
  --创建日期   : 20211124
  --功能描述   : 指标描述
  /*根据戴媛需求，从兆尹取数，配合估值，fisp数据进行填充形成定向收入计算报表
  */
  --============================================================================
 IS

  V_BEGIN_DATE  VARCHAR(8) := TO_CHAR(NVL(I_BEGIN_DATE, 0));
  V_BEGIN_DATE1 VARCHAR(10) := SUBSTR(NVL(I_BEGIN_DATE, 0), 0, 4) || '-' ||
                               SUBSTR(NVL(I_BEGIN_DATE, 0), 5, 2) || '-' ||
                               SUBSTR(NVL(I_BEGIN_DATE, 0), 7, 2);

BEGIN 
  /* 
  CREATE table GGZB.TBB_DXSRJS_DM ( 
  账套号 VARCHAR2(12), 
         计划编码 VARCHAR2(20) not null, 
         计划名称 VARCHAR2(100), 
         期数 VARCHAR2(12), 
         管理方式 VARCHAR2(8), 
         资产三级分类 VARCHAR2(100), 
         业务来源 VARCHAR2(100), 
         委托人 VARCHAR2(100), 
         交易对手 VARCHAR2(100), 
         管理费率 number, 
         起始运作日 char(10), 
         管理费率_变动费率1 number, 
         管理费率_变动日期1 char(10), 
         管理费率_变动费率2 number, 
         管理费率_变动日期2 char(10), 
         管理费率_变动费率3 number, 
         管理费率_变动日期3 char(10), 
         委托金额 number, 
         未清算金额 number, 
         是否清算 VARCHAR2(5), 
         "清算(还款)日" char(10), 
         最迟到期日 char(10), 
         年计息天数 VARCHAR2(12), 
         "业务归属部门(分公司)" VARCHAR2(100), 
         "业务归属部门(计财口径-营业部)" VARCHAR2(100), 
         营业部代码 VARCHAR2(20), 
         本年日均规模 number, 
         经办人 VARCHAR2(20), 
         是否创新 VARCHAR2(20), 
         行内OR行外 VARCHAR2(20), 
         起始运作日分类 VARCHAR2(20), 
         管理费率分类 VARCHAR2(20), 
         截止计算日 char(10), 
         本年计算天数 number, 
         分段计息天数1 number, 
         分段计息收入1 number, 
         分段计息天数2 number, 
         分段计息收入2 number, 
         分段计息天数3 number, 
         分段计息收入3 number, 
         其他项备注 VARCHAR2(100), 
         "累计管理费（含税）" number, 
         "本年度管理费（含税）" number, 
         "总累计管理费（含税）" number, 
         标识 VARCHAR2(10), 
         管理费是否与产品净值挂钩 VARCHAR2(10), 
         两费是否与净值有关 VARCHAR2(10), 
         资金方 VARCHAR2(100), 
         资产方 VARCHAR2(100), 
         CPCODE VARCHAR2(20)) 
          
   
  */ 
  /*--建表过程 
  create table ggzb.dcx_MYJOYIN2_QIYE_INFO  as 
   SELECT O_NAME,O_CODE 
   FROM MYJOYIN2.QIYE_INFO@ZYZG 
    
  create table ggzb.dcx_TDEPART_INFO as 
  SELECT O_CODE,O_NAME 
                  FROM MYJOYIN2.TDEPART_INFO@ZYZG 
   
  create table ggzb.dcx_TUSER_INFO as                 
  SELECT O_CODE,O_NAME 
                  FROM MYJOYIN2.TUSER_INFO@ZYZG 
                   
  create table ggzb.dcx_TFUND_WTR_XZ as 
  SELECT o_code,O_NAME 
  FROM MYJOYIN2.TFUND_WTR_XZ@ZYZG LY 
  where d_flag=0            
   
  create table ggzb.dcx_TFUND_REPAYSCHEDULE as 
  select O_CODE,PAYDATE,CASHAMT from MYJOYIN2.TFUND_REPAYSCHEDULE@ZYZG 
  where D_FLAG = 0 and PLANSTATUS = 0 
   
   
  CREATE TABLE GGZB.DCX_TTAB_COL_DEFINE_ENUM AS 
  SELECT ENUM_LABEL,ENUM_VALUE FROM MYJOYIN2.TTAB_COL_DEFINE_ENUM@ZYZG 
   WHERE TAB_CODE = 'INFO_00022680' 
                 AND COL_CODE = 'ZCSJFL' 
                  
  CREATE table ggzb.dcx_TPROJECT_INFO as 
  SELECT O_CODE,D_FLAG,ZCF,ZJF,GLFL,QSYZR,JYDFMC,FGSYWGSBM,YYBYWGSBM,ZCDQR,FOR_JXJC,YWLX,ZCSJFL,SFCX,AMT,QS,JBR,YYBDM 
  FROM MYJOYIN2.TPROJECT_INFO@ZYZG 
  WHERE D_FLAG = 0 
  --20220824，为了计算营业部代码新增 
  CREATE TABLE GGZB.DCX_TTAB_DEFINE AS 
    SELECT  TAB_CODE ,TABLENAME FROM  MYJOYIN2.TTAB_DEFINE@ZYZG 
     
    CREATE TABLE GGZB.DCX_TTAB_COL_DEFINE AS 
    SELECT COL_FORMAT,TAB_CODE,COL_CODE FROM MYJOYIN2.TTAB_COL_DEFINE@ZYZG 
     
       CREATE TABLE GGZB.DCX_TTAB_ENUM_PARAM_CHILD AS 
    SELECT LABEL,PARAM_CODE,VALUE FROM MYJOYIN2.TTAB_ENUM_PARAM_CHILD@ZYZG  
     
      CREATE TABLE GGZB.DCX_TTAB_COL_DEFINE_ENUM_1 AS 
    SELECT enum_label,col_code,tab_code,enum_value FROM MYJOYIN2.TTAB_COL_DEFINE_ENUM@ZYZG 
  */ 
  DELETE FROM GGZB.DCX_MYJOYIN2_QIYE_INFO; 
  INSERT INTO GGZB.DCX_MYJOYIN2_QIYE_INFO 
    SELECT O_NAME, O_CODE FROM MYJOYIN2.QIYE_INFO@ZYZG; 
 
  DELETE FROM GGZB.DCX_TDEPART_INFO; 
  INSERT INTO GGZB.DCX_TDEPART_INFO 
    SELECT O_CODE, O_NAME FROM MYJOYIN2.TDEPART_INFO@ZYZG; 
 
  DELETE FROM GGZB.DCX_TUSER_INFO; 
  INSERT INTO GGZB.DCX_TUSER_INFO 
    SELECT O_CODE, O_NAME FROM MYJOYIN2.TUSER_INFO@ZYZG; 
 
  DELETE FROM GGZB.DCX_TFUND_WTR_XZ; 
  INSERT INTO GGZB.DCX_TFUND_WTR_XZ 
    SELECT O_CODE, O_NAME 
      FROM MYJOYIN2.TFUND_WTR_XZ@ZYZG LY 
     WHERE D_FLAG = 0; 
 
  DELETE FROM GGZB.DCX_TFUND_REPAYSCHEDULE; 
  INSERT INTO GGZB.DCX_TFUND_REPAYSCHEDULE 
    SELECT O_CODE, PAYDATE, CASHAMT 
      FROM MYJOYIN2.TFUND_REPAYSCHEDULE@ZYZG 
     WHERE D_FLAG = 0 
       AND PLANSTATUS = 0; 
 
  DELETE FROM GGZB.DCX_TTAB_COL_DEFINE_ENUM; 
  INSERT INTO GGZB.DCX_TTAB_COL_DEFINE_ENUM 
    SELECT ENUM_LABEL, ENUM_VALUE 
      FROM MYJOYIN2.TTAB_COL_DEFINE_ENUM@ZYZG 
     WHERE TAB_CODE = 'INFO_00022680' 
       AND COL_CODE = 'ZCSJFL'; 
  --- 
  DELETE FROM GGZB.DCX_TTAB_DEFINE; 
  INSERT INTO GGZB.DCX_TTAB_DEFINE 
    SELECT TAB_CODE, TABLENAME FROM MYJOYIN2.TTAB_DEFINE@ZYZG; 
 
  DELETE FROM GGZB.DCX_TTAB_COL_DEFINE; 
  INSERT INTO GGZB.DCX_TTAB_COL_DEFINE 
    SELECT COL_FORMAT, TAB_CODE, COL_CODE 
      FROM MYJOYIN2.TTAB_COL_DEFINE@ZYZG; 
 
  DELETE FROM GGZB.DCX_TTAB_ENUM_PARAM_CHILD; 
  INSERT INTO GGZB.DCX_TTAB_ENUM_PARAM_CHILD 
    SELECT LABEL, PARAM_CODE, VALUE 
      FROM MYJOYIN2.TTAB_ENUM_PARAM_CHILD@ZYZG; 
 
  DELETE FROM GGZB.DCX_TTAB_COL_DEFINE_ENUM_1; 
  INSERT INTO GGZB.DCX_TTAB_COL_DEFINE_ENUM_1 
    SELECT ENUM_LABEL, COL_CODE, TAB_CODE, ENUM_VALUE 
      FROM MYJOYIN2.TTAB_COL_DEFINE_ENUM@ZYZG; 
 
  DELETE FROM GGZB.DCX_TPROJECT_INFO; 
  INSERT INTO GGZB.DCX_TPROJECT_INFO 
    SELECT O_CODE, 
           D_FLAG, 
           ZCF, 
           ZJF, 
           GLFL, 
           QSYZR, 
           JYDFMC, 
           FGSYWGSBM, 
           YYBYWGSBM, 
           ZCDQR, 
           FOR_JXJC, 
           YWLX, 
           ZCSJFL, 
           SFCX, 
           AMT, 
           QS, 
           JBR, 
           GGZB.GET_LABEL_BY_VALUE(YYBDM, 'TPROJECT_INFO', 'YYBDM') YYBDM 
      FROM MYJOYIN2.TPROJECT_INFO@ZYZG 
     WHERE D_FLAG = 0; 
  DELETE FROM GGZB.TBB_DXSRJS_DM; 
  INSERT INTO GGZB.TBB_DXSRJS_DM 
    (账套号, 
     计划编码, 
     计划名称, 
     期数, 
     管理方式, 
     资产三级分类, 
     业务来源, 
     委托人, 
     交易对手, 
     管理费率, 
     起始运作日, 
     管理费率_变动费率1, 
     管理费率_变动日期1, 
     管理费率_变动费率2, 
     管理费率_变动日期2, 
     管理费率_变动费率3, 
     管理费率_变动日期3, 
     委托金额, 
     未清算金额, 
     是否清算, 
     "清算(还款)日", 
     最迟到期日, 
     年计息天数, 
     "业务归属部门(分公司)", 
     "业务归属部门(计财口径-营业部)", 
     营业部代码, 
     本年日均规模, 
     经办人, 
     是否创新, 
     行内OR行外, 
     起始运作日分类, 
     管理费率分类, 
     截止计算日, 
     资金方, 
     资产方, 
     CPCODE) 
    SELECT A.CPGZZTBH 账套号, 
           RP.O_CODE 计划编码, 
           A.SNAME 计划名称, 
           C.QS 期数, 
           DECODE(A.GLFS, 1, '主动管理', 2, '被动管理') 管理方式, 
           C.ZCSJFL 资产三级分类, 
           C.YWLX 业务来源, 
           NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                 FROM GGZB.DCX_TFUND_WTR_XZ LY 
                WHERE LY.O_CODE = A.O_CODE), 
               A.WTRQ) 委托人, 
           (SELECT A.O_NAME 
              FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
             WHERE A.O_CODE = C.JYDFMC 
               AND ROWNUM = 1) 交易对手, 
           C.GLFL 管理费率, 
           C.QSYZR 起始运作日, 
           GGZB.ZY_GET_FEE_RATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 1) / 100 管理费率_变动费率1, 
           GGZB.ZY_GET_FEE_DATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 1) 管理费率_变动日期1, 
           GGZB.ZY_GET_FEE_RATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 2) / 100 管理费率_变动费率2, 
           GGZB.ZY_GET_FEE_DATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 2) 管理费率_变动日期2, 
            
           GGZB.ZY_GET_FEE_RATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 3) / 100 管理费率_变动费率3, 
           GGZB.ZY_GET_FEE_DATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 3) 管理费率_变动日期3, 
           RP.CASHAMT 委托金额, 
           0 未清算金额, 
           '是' 是否清算, 
           RP.PAYDATE "清算(还款)日", 
           C.ZCDQR 最迟到期日, 
           C.FOR_JXJC 年计息天数, 
           (SELECT T.O_NAME 
              FROM GGZB.DCX_TDEPART_INFO T 
             WHERE T.O_CODE = C.FGSYWGSBM) "业务归属部门(分公司)", 
           (SELECT T.O_NAME 
              FROM GGZB.DCX_TDEPART_INFO T 
             WHERE T.O_CODE = C.YYBYWGSBM) "业务归属部门(计财口径-营业部)", 
           C.YYBDM 营业部代码, 
           /* RP.CASHAMT* C.FOR_JXJC/*/ 
           0 本年日均规模, 
           (SELECT T.O_NAME 
              FROM GGZB.DCX_TUSER_INFO T 
             WHERE T.O_CODE = C.JBR) 经办人, 
           DECODE(C.SFCX, '0', '否', 1, '是') 是否创新, 
           CASE 
             WHEN NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%华福%' OR 
                  NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%兴瀚%' OR 
                  (NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%兴银%' ) OR 
                  NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%昕泰%' OR 
                 --要兴业但不要兴业证券 
                  (NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                         FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                       A.WTRQ) LIKE '%兴业%' AND 
                   NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                         FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                       A.WTRQ) NOT LIKE '%兴业证券%' AND 
                   NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                         FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                       A.WTRQ) NOT LIKE '%兴业全球基金%') OR 
                 --对手方 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%华福%' OR 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%兴瀚%' OR 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%兴银%' OR 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%昕泰%' OR 
                 --要兴业但不要兴业证券 
                  ((SELECT A.O_NAME 
                      FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                     WHERE A.O_CODE = C.JYDFMC 
                       AND ROWNUM = 1) LIKE '%兴业%' AND 
                   (SELECT A.O_NAME 
                      FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                     WHERE A.O_CODE = C.JYDFMC 
                       AND ROWNUM = 1) NOT LIKE '%兴业证券%' AND 
                   (SELECT A.O_NAME 
                      FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                     WHERE A.O_CODE = C.JYDFMC 
                       AND ROWNUM = 1) NOT LIKE '%兴业全球基金%') THEN 
              '行内' 
             ELSE 
              '行外' 
           END 行内OR行外, 
           CASE 
             WHEN C.QSYZR <= '2015-12-31' THEN 
              '1' 
             WHEN C.QSYZR > '2015-12-31' AND C.QSYZR <= '2017-03-14' THEN 
              '2' 
             WHEN C.QSYZR > '2017-03-14' AND C.QSYZR <= '2017-07-31' THEN 
              '3' 
             WHEN C.QSYZR > '2017-07-31' THEN 
              '4' 
             ELSE 
              '-99' 
           END 起始运作日分类, 
           CASE 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL <= 0.0003 THEN 
              '1' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0003 AND 
                  C.GLFL < 0.0004 THEN 
              '2' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0004 AND 
                  C.GLFL < 0.0005 THEN 
              '3' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0005 AND 
                  C.GLFL < 0.0006 THEN 
              '4' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0006 THEN 
              '5' 
             WHEN C.QSYZR <= '2017-03-14' THEN 
              '0' 
             ELSE 
              '-99' 
           END 管理费率分类, 
           V_BEGIN_DATE1 截止计算日, 
           C.ZJF 资金方, 
           C. ZCF 资产方, 
           A.CPCODE 
      FROM GGZB.DCX_TFUND_REPAYSCHEDULE RP 
     INNER JOIN GGZB.DCX_TPROJECT_INFO C 
        ON RP.O_CODE = C.O_CODE 
     INNER JOIN MYJOYIN2.TFUND_PROJECT_DETAIL@ZYZG B 
        ON B.ASSET_CODE = C.O_CODE 
     INNER JOIN MYJOYIN2.TFUND_INFO@ZYZG A 
        ON A.O_CODE = B.O_CODE 
     
     WHERE A.D_FLAG = B.D_FLAG 
       AND B.D_FLAG = C.D_FLAG 
       AND C.D_FLAG = 0 
       AND CASHAMT > 0; 
  COMMIT; 
 
  INSERT INTO GGZB.TBB_DXSRJS_DM 
    (账套号, 
     计划编码, 
     计划名称, 
     期数, 
     管理方式, 
     资产三级分类, 
     业务来源, 
     委托人, 
     交易对手, 
     管理费率, 
     起始运作日, 
     管理费率_变动费率1, 
     管理费率_变动日期1, 
     管理费率_变动费率2, 
     管理费率_变动日期2, 
     管理费率_变动费率3, 
     管理费率_变动日期3, 
     委托金额, 
     未清算金额, 
     是否清算, 
     "清算(还款)日", 
     最迟到期日, 
     年计息天数, 
     "业务归属部门(分公司)", 
     "业务归属部门(计财口径-营业部)", 
     营业部代码, 
     本年日均规模, 
     经办人, 
     是否创新, 
     行内OR行外, 
     起始运作日分类, 
     管理费率分类, 
     截止计算日, 
     其他项备注, 
     "累计管理费（含税）", 
     "本年度管理费（含税）", 
     标识, 
     资金方, 
     资产方, 
     CPCODE) 
   
    SELECT A.CPGZZTBH 账套号, 
           C.O_CODE 计划编码, 
           A.SNAME 计划名称, 
           C.QS 期数, 
           DECODE(A.GLFS, 1, '主动管理', 2, '被动管理') 管理方式, 
           C.ZCSJFL 资产三级分类, 
           C.YWLX 业务来源, 
           NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                 FROM GGZB.DCX_TFUND_WTR_XZ LY 
                WHERE LY.O_CODE = A.O_CODE), 
               A.WTRQ) 委托人, 
           (SELECT A.O_NAME 
              FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
             WHERE A.O_CODE = C.JYDFMC 
               AND ROWNUM = 1) 交易对手, 
           C.GLFL 管理费率, 
           C.QSYZR 起始运作日, 
           GGZB.ZY_GET_FEE_RATE(C.O_CODE, 
                                C.QSYZR, 
                                (SELECT JYR 
                                   FROM GGZB.TXTJYR 
                                  WHERE ZRR = V_BEGIN_DATE), 
                                'F2', 
                                1) / 100 管理费率_变动费率1, 
           GGZB.ZY_GET_FEE_DATE(C.O_CODE, 
                                C.QSYZR, 
                                (SELECT JYR 
                                   FROM GGZB.TXTJYR 
                                  WHERE ZRR = V_BEGIN_DATE), 
                                'F2', 
                                1) 管理费率_变动日期1, 
           GGZB.ZY_GET_FEE_RATE(C.O_CODE, 
                                C.QSYZR, 
                                (SELECT JYR 
                                   FROM GGZB.TXTJYR 
                                  WHERE ZRR = V_BEGIN_DATE), 
                                'F2', 
                                2) / 100 管理费率_变动费率2, 
           GGZB.ZY_GET_FEE_DATE(C.O_CODE, 
                                C.QSYZR, 
                                (SELECT JYR 
                                   FROM GGZB.TXTJYR 
                                  WHERE ZRR = V_BEGIN_DATE), 
                                'F2', 
                                2) 管理费率_变动日期2, 
            
           GGZB.ZY_GET_FEE_RATE(C.O_CODE, 
                                C.QSYZR, 
                                (SELECT JYR 
                                   FROM GGZB.TXTJYR 
                                  WHERE ZRR = V_BEGIN_DATE), 
                                'F2', 
                                3) / 100 管理费率_变动费率3, 
           GGZB.ZY_GET_FEE_DATE(C.O_CODE, 
                                C.QSYZR, 
                                (SELECT JYR 
                                   FROM GGZB.TXTJYR 
                                  WHERE ZRR = V_BEGIN_DATE), 
                                'F2', 
                                3) 管理费率_变动日期3, 
           C.AMT - C.CASHAMT 委托金额, 
           C.AMT - C.CASHAMT 未清算金额, 
           '否' 是否清算, 
           '' "清算(还款)日", 
           C.ZCDQR 最迟到期日, 
           C.FOR_JXJC 年计息天数, 
           (SELECT T.O_NAME 
              FROM GGZB.DCX_TDEPART_INFO T 
             WHERE T.O_CODE = C.FGSYWGSBM) "业务归属部门(分公司)", 
           (SELECT T.O_NAME 
              FROM GGZB.DCX_TDEPART_INFO T 
             WHERE T.O_CODE = C.YYBYWGSBM) "业务归属部门(计财口径-营业部)", 
           C.YYBDM 营业部代码, 
           0 本年日均规模, 
           (SELECT T.O_NAME 
              FROM GGZB.DCX_TUSER_INFO T 
             WHERE T.O_CODE = C.JBR) 经办人, 
           DECODE(C.SFCX, '0', '否', 1, '是') 是否创新, 
           CASE 
             WHEN NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%华福%' OR 
                  NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%兴瀚%' OR 
                  NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%兴银%' OR 
                  NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                        FROM GGZB.DCX_TFUND_WTR_XZ LY 
                       WHERE LY.O_CODE = A.O_CODE), 
                      A.WTRQ) LIKE '%昕泰%' OR 
                 --要兴业但不要兴业证券 
                  (NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                         FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                       A.WTRQ) LIKE '%兴业%' AND 
                   NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                         FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                       A.WTRQ) NOT LIKE '%兴业证券%' AND 
                   NVL((SELECT TO_CHAR(WM_CONCAT(LY.O_NAME)) 
                         FROM GGZB.DCX_TFUND_WTR_XZ LY 
                        WHERE LY.O_CODE = A.O_CODE), 
                       A.WTRQ) NOT LIKE '%兴业全球基金%') OR 
                 --对手方 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%华福%' OR 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%兴瀚%' OR 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%兴银%' OR 
                  (SELECT A.O_NAME 
                     FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                    WHERE A.O_CODE = C.JYDFMC 
                      AND ROWNUM = 1) LIKE '%昕泰%' OR 
                 --要兴业但不要兴业证券 
                  ((SELECT A.O_NAME 
                      FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                     WHERE A.O_CODE = C.JYDFMC 
                       AND ROWNUM = 1) LIKE '%兴业%' AND 
                   (SELECT A.O_NAME 
                      FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                     WHERE A.O_CODE = C.JYDFMC 
                       AND ROWNUM = 1) NOT LIKE '%兴业证券%' AND 
                   (SELECT A.O_NAME 
                      FROM GGZB.DCX_MYJOYIN2_QIYE_INFO A 
                     WHERE A.O_CODE = C.JYDFMC 
                       AND ROWNUM = 1) NOT LIKE '%兴业全球基金%') THEN 
              '行内' 
             ELSE 
              '行外' 
           END 行内OR行外, 
           CASE 
             WHEN C.QSYZR <= '2015-12-31' THEN 
              '1' 
             WHEN C.QSYZR > '2015-12-31' AND C.QSYZR <= '2017-03-14' THEN 
              '2' 
             WHEN C.QSYZR > '2017-03-14' AND C.QSYZR <= '2017-07-31' THEN 
              '3' 
             WHEN C.QSYZR > '2017-07-31' THEN 
              '4' 
             ELSE 
              '-99' 
           END 起始运作日分类, 
           CASE 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL <= 0.0003 THEN 
              '1' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0003 AND 
                  C.GLFL < 0.0004 THEN 
              '2' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0004 AND 
                  C.GLFL < 0.0005 THEN 
              '3' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0005 AND 
                  C.GLFL < 0.0006 THEN 
              '4' 
             WHEN C.QSYZR > '2017-03-14' AND C.GLFL > 0.0006 THEN 
              '5' 
             WHEN C.QSYZR <= '2017-03-14' THEN 
              '0' 
             ELSE 
              '-99' 
           END 管理费率分类, 
           V_BEGIN_DATE1 截止计算日, 
           '无' 其他项备注, 
           '' "累计管理费（含税）", 
           '' "本年度管理费（含税）", 
           CASE 
             WHEN A.GLFS = 2 THEN 
              '-' 
             ELSE 
              CASE 
                WHEN C.QSYZR LIKE '2019%' OR 
                     GGZB.ZY_GET_FEE_DATE(C.O_CODE, 
                                          C.QSYZR, 
                                          (SELECT JYR 
                                             FROM GGZB.TXTJYR 
                                            WHERE ZRR = V_BEGIN_DATE), 
                                          'F2', 
                                          2) LIKE '2019%' THEN 
                 (SELECT BS FROM GGZB.TTZ_ABLCPMC WHERE CPMC = A.SNAME) 
                WHEN C.QSYZR >= SUBSTR((SELECT JYR 
                                         FROM GGZB.TXTJYR 
                                        WHERE ZRR = V_BEGIN_DATE), 
                                       0, 
                                       4) || '-01-01' OR 
                     GGZB.ZY_GET_FEE_DATE(C.O_CODE, 
                                          C.QSYZR, 
                                          (SELECT JYR 
                                             FROM GGZB.TXTJYR 
                                            WHERE ZRR = V_BEGIN_DATE), 
                                          'F2', 
                                          2) >= 
                     SUBSTR((SELECT JYR 
                              FROM GGZB.TXTJYR 
                             WHERE ZRR = V_BEGIN_DATE), 
                            0, 
                            4) || '-01-01' THEN 
                 '3' 
              END 
           END 标识, 
           C.ZJF 资金方, 
           C. ZCF 资产方, 
           A.CPCODE 
      FROM (SELECT P.*, 
                   (SELECT NVL(SUM(RP.CASHAMT), 0) 
                      FROM GGZB.DCX_TFUND_REPAYSCHEDULE RP 
                     WHERE O_CODE = P.O_CODE) CASHAMT 
              FROM GGZB.DCX_TPROJECT_INFO P) C 
     INNER JOIN MYJOYIN2.TFUND_PROJECT_DETAIL@ZYZG B 
        ON B.ASSET_CODE = C.O_CODE 
     INNER JOIN MYJOYIN2.TFUND_INFO@ZYZG A 
        ON A.O_CODE = B.O_CODE 
     WHERE A.D_FLAG = B.D_FLAG 
       AND B.D_FLAG = C.D_FLAG 
       AND C.D_FLAG = 0 
       AND C.AMT - C.CASHAMT > 0 
       AND C.AMT > C.CASHAMT; 
  --账套号 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.账套号 = 
         (SELECT B.L_ID 
            FROM HSFA.TSYSINFO@GZXT B 
           WHERE B.VC_NAME = A.计划名称 
             AND ROWNUM < 2) 
   WHERE 截止计算日 = V_BEGIN_DATE1 
     AND A.账套号 IS NULL; 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.是否清算 = '否' 
   WHERE A.截止计算日 = V_BEGIN_DATE1 
     AND A."清算(还款)日" > A.截止计算日; 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "清算(还款)日" = NULL 
   WHERE A.是否清算 = '否' 
     AND 截止计算日 = V_BEGIN_DATE1; 
  --兆尹中存在清算还款日中有/的数据 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "清算(还款)日" = REPLACE("清算(还款)日", '/', '-') 
   WHERE A.是否清算 = '是' 
     AND a."清算(还款)日" LIKE '%/%' 
     AND 截止计算日 = V_BEGIN_DATE1; 
 
  --本年计算天数 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.本年计算天数 = 
         (SELECT COUNT(DISTINCT ZRR) 
            FROM GGZB.TXTJYR 
           WHERE ZRR <= REPLACE(LEAST(A.截止计算日, a."清算(还款)日"), '-') 
             AND ZRR >= REPLACE(GREATEST(SUBSTR(A.截止计算日, 0, 4) || '-01-01', 
                                         A.起始运作日), 
                                '-')) - 1 
   WHERE A.是否清算 = '是' 
     AND A.截止计算日 = V_BEGIN_DATE1; 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.本年计算天数 = 
         (SELECT COUNT(DISTINCT ZRR) 
            FROM GGZB.TXTJYR 
           WHERE ZRR <= REPLACE(A.截止计算日, '-') 
             AND ZRR >= REPLACE(GREATEST(SUBSTR(A.截止计算日, 0, 4) || '-01-01', 
                                         A.起始运作日), 
                                '-')) 
   WHERE A.是否清算 = '否' 
     AND A.截止计算日 = V_BEGIN_DATE1; 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.本年计算天数 = 0 
   WHERE A.本年计算天数 < 0 
     AND A.截止计算日 = V_BEGIN_DATE1; 
 
  --分段计息天数1 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.分段计息天数1 = CASE 
                       WHEN A.管理费率_变动日期1 >= SUBSTR(A.截止计算日, 0, 4) || '-01-01' THEN 
                        LEAST(TO_DATE(A.管理费率_变动日期2, 'YYYY-MM-DD'), 
                              TO_DATE(A."清算(还款)日", 'YYYY-MM-DD')) - 
                        TO_DATE(A.管理费率_变动日期1, 'YYYY-MM-DD') 
                       ELSE 
                        0 
                     END 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --分段计息收入1 
  UPDATE GGZB.TBB_DXSRJS_DM A 
   
     SET A.分段计息收入1 = ROUND(TO_NUMBER(NVL(A.分段计息天数1, 0) * NVL(A.委托金额, 0) * 
                                     NVL(A.管理费率_变动费率1, 0) / 
                                     TO_NUMBER(SUBSTR(A.年计息天数, 3, 3))), 
                           2) 
   
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --分段计息天数2 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.分段计息天数2 = CASE 
                       WHEN A.管理费率_变动日期2 >= SUBSTR(A.截止计算日, 0, 4) || '-01-01' THEN 
                        TO_DATE(A.管理费率_变动日期2, 'YYYY-MM-DD') - 
                        GREATEST(TO_DATE(A.管理费率_变动日期1, 'YYYY-MM-DD'), 
                                 TO_DATE(SUBSTR(A.截止计算日, 0, 4) || '-01-01', 
                                         'YYYY-MM-DD')) 
                       ELSE 
                        0 
                     END 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --分段计息收入2 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.分段计息收入2 = ROUND(TO_NUMBER(NVL(A.分段计息天数2, 0) * NVL(A.委托金额, 0) * 
                                     NVL(A.管理费率_变动费率2, A.管理费率) / 
                                     TO_NUMBER(SUBSTR(A.年计息天数, 3, 3))), 
                           2) 
   
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --分段计息天数3 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.分段计息天数3 = 
         (A.本年计算天数 - A.分段计息天数1 - A.分段计息天数2) 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --分段计息收入3 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.分段计息收入3 = (CASE 
                       WHEN A.管理费率_变动费率3 IS NULL THEN 
                        ROUND(TO_NUMBER(NVL(A.分段计息天数3, 0) * NVL(A.委托金额, 0) * 
                                        NVL(A.管理费率, 0) / 
                                        TO_NUMBER(SUBSTR(A.年计息天数, 3, 3))), 
                              2) 
                       ELSE 
                        ROUND(TO_NUMBER(NVL(A.分段计息天数3, 0) * NVL(A.委托金额, 0) * 
                                        NVL(A.管理费率_变动费率3 / 100, 0) / 
                                        (SELECT COUNT(*) 
                                           FROM GGZB.TXTJYR 
                                          WHERE ZRR BETWEEN 
                                                SUBSTR(A.截止计算日, 0, 4) || 
                                                '0101' AND 
                                                REPLACE(TO_CHAR(A.截止计算日), 
                                                        '-'))), 
                              2) 
                     END) 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --本年日均规模 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.本年日均规模 = ROUND(A.委托金额 * A.本年计算天数 / 
                          (SELECT COUNT(*) 
                             FROM GGZB.TXTJYR 
                            WHERE ZRR BETWEEN SUBSTR(A.截止计算日, 0, 4) || '0101' AND 
                                  REPLACE(A.截止计算日, '-')) / 100000000, 
                          6) 
   
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --累计管理费（含税） 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "累计管理费（含税）"       = ROUND(TO_NUMBER((SELECT SUM(NVL(EN_DEBIT, 0)) 
                                               FROM HSFA.TVOUCHERS@GZXT 
                                              WHERE L_FUNDID = A.账套号 
                                                AND VC_CODE = '640301' 
                                                AND TO_CHAR(D_MAKE, 
                                                            'yyyy-mm-dd') >= 
                                                    SUBSTR(A.截止计算日, 
                                                           0, 
                                                           4) || '-01-01' 
                                                AND TO_CHAR(D_MAKE, 
                                                            'yyyy-mm-dd') <= 
                                                    A.截止计算日)), 
                                   2), 
         A.管理费是否与产品净值挂钩 = '是' 
   WHERE A.账套号 IN (SELECT L_ZTBH 
                     FROM HSFA.VJK_WBFK_LFFLSZ@GZXT 
                    WHERE D_KSRQ < TO_DATE(V_BEGIN_DATE, 'yyyy-mm-dd') 
                      AND (D_JSRQ IS NULL OR 
                          D_JSRQ > TO_DATE(V_BEGIN_DATE, 'yyyy-mm-dd')) 
                      AND VC_JFKM = '640301' 
                      AND EN_FL > 0) 
     AND 截止计算日 = V_BEGIN_DATE1; 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "累计管理费（含税）"       = ROUND(TO_NUMBER((NVL(分段计息收入3, 0) + 
                                             NVL(分段计息收入2, 0) + 
                                             NVL(分段计息收入1, 0))), 
                                   2), 
         A.管理费是否与产品净值挂钩 = '否' 
   WHERE (A.账套号 NOT IN (SELECT L_ZTBH 
                          FROM HSFA.VJK_WBFK_LFFLSZ@GZXT 
                         WHERE D_KSRQ < TO_DATE(V_BEGIN_DATE, 'yyyy-mm-dd') 
                           AND (D_JSRQ IS NULL OR 
                               D_JSRQ > TO_DATE(V_BEGIN_DATE, 'yyyy-mm-dd')) 
                           AND VC_JFKM = '640301' 
                           AND EN_FL > 0) OR A.账套号 IS NULL) 
     AND 截止计算日 = V_BEGIN_DATE1; 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "累计管理费（含税）" = CASE 
                         WHEN (SELECT SUM(B.本年日均规模) 
                                 FROM GGZB.TBB_DXSRJS_DM B 
                                WHERE B.截止计算日 = V_BEGIN_DATE1 
                                  AND b."累计管理费（含税）" <> 0 
                                  AND B.管理费是否与产品净值挂钩 = '是' 
                                  AND B.CPCODE = A.CPCODE) = 0 THEN 
                          0 
                         ELSE 
                          ROUND("累计管理费（含税）" * 本年日均规模 / 
                                (SELECT SUM(B.本年日均规模) 
                                   FROM GGZB.TBB_DXSRJS_DM B 
                                  WHERE B.截止计算日 = V_BEGIN_DATE1 
                                    AND b."累计管理费（含税）" <> 0 
                                    AND B.管理费是否与产品净值挂钩 = '是' 
                                    AND B.CPCODE = A.CPCODE), 
                                2) 
                       END 
   WHERE A.截止计算日 = V_BEGIN_DATE1 
     AND a."累计管理费（含税）" <> 0 
     AND A.管理费是否与产品净值挂钩 = '是'; 
 
  --本年度管理费（含税）  第2期新增 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "本年度管理费（含税）" = ROUND(ROUND(TO_NUMBER((SELECT SUM(NVL(EN_DEBIT, 0)) 
                                                FROM HSFA.TVOUCHERS@GZXT 
                                               WHERE L_FUNDID = A.账套号 
                                                 AND VC_CODE = '640301' 
                                                 AND L_STATE <> '32' 
                                                 AND TO_CHAR(D_MAKE, 
                                                             'yyyy-mm-dd') >= 
                                                     SUBSTR(A.截止计算日, 
                                                            0, 
                                                            4) || '-01-01' 
                                                     
                                                 AND TO_CHAR(D_MAKE, 
                                                             'yyyy-mm-dd') <= 
                                                     A.截止计算日)), 
                                    2) * 本年日均规模 / 
                              (SELECT SUM(B.本年日均规模) 
                                 FROM GGZB.TBB_DXSRJS_DM B 
                                WHERE B.截止计算日 = V_BEGIN_DATE1 
                                  AND B.CPCODE = A.CPCODE HAVING 
                                SUM(B.本年日均规模) <> 0), 
                              2) 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  --本年度管理费（含税）  特殊产品计0   2022/5/20 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "本年度管理费（含税）" = 0 
   WHERE A.计划名称 IN 
         ('闽2017-027号', '浙商2016-011号', '兴隆收益1号', '沪2017-001号'); 
 
  --总累计管理费（含税）  本年度管理费的和 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET "总累计管理费（含税）" = ROUND((SELECT SUM("本年度管理费（含税）") 
                                FROM GGZB.TBB_DXSRJS_DM B 
                               WHERE B.计划名称 = A.计划名称 
                                 AND B.截止计算日 = V_BEGIN_DATE1), 
                              2) 
   WHERE A.截止计算日 = V_BEGIN_DATE1; 
 
  --标识 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.标识 = CASE 
                  WHEN A.管理方式 = '被动管理' THEN 
                   NULL 
                  ELSE 
                  /* 
                  CASE 
                    WHEN A.起始运作日 LIKE '2019%' OR A.管理费率_变动日期2 LIKE '2019%' 
                      OR A.管理费率_变动日期3 LIKE '2019%' 
                       THEN 
                     (SELECT BS FROM GGZB.TTZ_ABLCPMC WHERE CPMC = A.计划名称) 
                    WHEN A.起始运作日 >= '2020-01-01' OR 
                         A.管理费率_变动日期2 >= '2020-01-01' 
                         OR A.管理费率_变动日期3 >= '2020-01-01' THEN */ 
                   '3' 
                -- END 
                END 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.标识 = 
         (SELECT TO_CHAR(WM_CONCAT(ZCLB)) 
            FROM (SELECT DISTINCT CPDM, 
                                  (CASE 
                                    WHEN ZCLB > 200 AND ZCLB <> '212' THEN 
                                     '非标' 
                                   
                                    ELSE 
                                     '标' 
                                  END) ZCLB 
                    FROM (SELECT CPDM, QMSZ, PERIOD, ZCLB 
                            FROM KGRP.R_CISP2_A1026@JGBS_ZG
                           WHERE QMSZ <> 0 
                             AND PERIOD >= 20210101)) 
           
           WHERE CPDM = A.CPCODE) 
   WHERE 截止计算日 = V_BEGIN_DATE1 
     AND 标识 = '3'; 
  /* 
  --特殊产品记为空白 
  UPDATE GGZB.TBB_DXSRJS_DM A 
  SET A.标识 = ''  
  WHERE 截止计算日 = V_BEGIN_DATE1 AND A.计划名称 in ('沪2017-001号','京2016-009号','兴隆收益1号','兴隆收益6号'); 
  --特殊产品（第三期） 
   UPDATE GGZB.TBB_DXSRJS_DM A 
  SET A.标识 = '标,非标'  
  WHERE 截止计算日 = V_BEGIN_DATE1 AND A.计划名称 in('贵2015-003号') 
  ; 
   UPDATE GGZB.TBB_DXSRJS_DM A 
  SET A.标识 = '标'  
  WHERE 截止计算日 = V_BEGIN_DATE1 AND A.计划名称 in('浙2015-014号','浙2015-015号') 
  ; 
  */ 
  --资产三级分类 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.资产三级分类 = 
         (SELECT AA.ENUM_LABEL 
            FROM GGZB.DCX_TTAB_COL_DEFINE_ENUM AA 
           WHERE AA.ENUM_VALUE = A.资产三级分类) 
   WHERE 截止计算日 = V_BEGIN_DATE1; 
 
  COMMIT; 
  --年计息收入的A去掉 2023.05.25 
  UPDATE GGZB.TBB_DXSRJS_DM A 
     SET A.年计息天数 = REPLACE(A.年计息天数, 'A/', ''); 
  COMMIT; 
  
  --2024.07.12增加绍兴银行特殊判断
   UPDATE GGZB.TBB_DXSRJS_DM A 
   SET A.行内OR行外='行外'
   WHERE A.委托人 LIKE '%绍兴银行%' or A.交易对手 LIKE '%绍兴银行%';
   commit;
  /* 
    --计算一次循环日志 
    INSERT INTO TLOGMSG 
      (ID, 
       RQ, 
       RUN_TIME, 
       RET_CODE, 
       RES_NODE, 
       PROC_NAME, 
       CMD, 
       EXEC_ROWCOUNT, 
       TIMES) 
      SELECT SEQ_LOGMSG.NEXTVAL, 
             TO_CHAR(SYSDATE, 'YYYYMMDD'), 
             TO_CHAR(SYSDATE, 'YYYYMMDD HH24:MI:SS'), 
             O_RET_CODE, 
             O_RET_MSG, 
             'SP_SJZB_DXSRJS_DM_XH', --过程名 
             'SP_SJZB_DXSRJS_DM_XH(' || I_BEGIN_DATE || ',' || I_END_DATE || ')', --参数名 
             0, 
             TRUNC((SYSDATE - V_DATETIME1) * 1440.00 * 60) --执行时间 
        FROM DUAL; 
  */ 
  --输出游标集 
  OPEN CUR_OUT FOR 
   
    SELECT 账套号, 
           计划编码, 
           计划名称, 
           期数, 
           管理方式, 
           资产三级分类, 
           业务来源, 
           委托人, 
           交易对手, 
           管理费率, 
           起始运作日, 
           管理费率_变动费率1, 
           管理费率_变动日期1, 
           管理费率_变动费率2, 
           管理费率_变动日期2, 
           管理费率_变动费率3, 
           管理费率_变动日期3, 
           委托金额, 
           未清算金额, 
           是否清算, 
           "清算(还款)日", 
           最迟到期日, 
           年计息天数, 
           "业务归属部门(分公司)", 
           "业务归属部门(计财口径-营业部)", 
           营业部代码, 
           本年日均规模, 
           经办人, 
           是否创新, 
           行内OR行外, 
           起始运作日分类, 
           管理费率分类, 
           截止计算日, 
           本年计算天数, 
           分段计息天数1, 
           分段计息收入1, 
           分段计息天数2, 
           分段计息收入2, 
           分段计息天数3, 
           分段计息收入3, 
           其他项备注, 
           "累计管理费（含税）", 
           "本年度管理费（含税）", 
           "总累计管理费（含税）", 
           标识, 
           管理费是否与产品净值挂钩, 
           两费是否与净值有关, 
           资金方, 
           资产方 
      FROM GGZB.TBB_DXSRJS_DM 
     WHERE 截止计算日 = V_BEGIN_DATE1 
     ORDER BY 账套号; 
  /* 
   
  --正常执行完成 
  O_RET_CODE := 0; 
  O_RET_MSG  := '执行完成'; 
  --总日志 
  INSERT INTO TLOGMSG 
    (ID, 
     RQ, 
     RUN_TIME, 
     RET_CODE, 
     RES_NODE, 
     PROC_NAME, 
     CMD, 
     EXEC_ROWCOUNT, 
     TIMES) 
    SELECT SEQ_LOGMSG.NEXTVAL, 
           TO_CHAR(SYSDATE, 'YYYYMMDD'), 
           TO_CHAR(SYSDATE, 'YYYYMMDD HH24:MI:SS'), 
           O_RET_CODE, 
           O_RET_MSG, 
           'SP_SJZB_DXSRJS_DM', --过程名 
           'SP_SJZB_DXSRJS_DM(' || I_BEGIN_DATE || ',' || I_END_DATE || ')', --参数名 
           0, 
           TRUNC((SYSDATE - V_DATETIME2) * 1440.00 * 60) --执行时间 
      FROM DUAL; 
      */ 
 
END SP_SJZB_DXSRJS_DM; 
