# 存储过程性能优化对比

## 原存储过程性能问题

### 1. 大量重复的UPDATE语句
**原代码：** 约30个独立的UPDATE语句，每个都包含复杂的子查询

**问题：**
- 每个UPDATE都要重新解析和执行
- 重复扫描相同的数据
- 大量的上下文切换

### 2. 频繁的函数调用
**原代码：** 多次重复调用 `GGZB.ZY_GET_FEE_RATE` 和 `GGZB.ZY_GET_FEE_DATE`

**问题：**
- 相同参数的函数被重复计算
- 函数调用开销累积

### 3. 缺乏索引支持
**问题：**
- `TBB_DXSRJS_DM` 表缺少关键字段索引
- 全表扫描导致性能低下

### 4. 远程链接性能
**问题：**
- 频繁使用 `@ZYZG`、`@GZXT`、`@JGBS_ZG` 远程链接
- 网络延迟影响性能

## 优化方案对比

### 优化1：索引创建
**原代码：** 无索引创建
```sql
-- 无索引支持
```

**优化后：**
```sql
BEGIN
  EXECUTE IMMEDIATE 'CREATE INDEX IDX_TBB_DXSRJS_DM_JZSJ ON GGZB.TBB_DXSRJS_DM(截止计算日)';
EXCEPTION
  WHEN OTHERS THEN NULL;
END;
```

### 优化2：批量UPDATE处理
**原代码：** 多个独立的UPDATE语句
```sql
UPDATE GGZB.TBB_DXSRJS_DM A SET A.分段计息天数1 = ... WHERE ...;
UPDATE GGZB.TBB_DXSRJS_DM A SET A.分段计息收入1 = ... WHERE ...;
UPDATE GGZB.TBB_DXSRJS_DM A SET A.分段计息天数2 = ... WHERE ...;
-- ... 约30个类似的UPDATE
```

**优化后：** 批量处理
```sql
FOR rec IN (SELECT ...批量计算所有字段...) LOOP
  UPDATE GGZB.TBB_DXSRJS_DM
  SET 分段计息天数1 = rec.分段计息天数1,
      分段计息收入1 = rec.分段计息收入1,
      分段计息天数2 = rec.分段计息天数2
  WHERE ROWID = rec.rid;
END LOOP;
```

### 优化3：函数结果缓存
**原代码：** 重复函数调用
```sql
GGZB.ZY_GET_FEE_RATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 1)
GGZB.ZY_GET_FEE_RATE(C.O_CODE, C.QSYZR, RP.PAYDATE, 'F2', 1) -- 重复调用
```

**优化后：** 预计算缓存
```sql
-- 预计算并缓存
v_fee_rate_cache(proj.O_CODE || '_F2_1') := 
  GGZB.ZY_GET_FEE_RATE(proj.O_CODE, proj.QSYZR, proj.PAYDATE, 'F2', 1);

-- 后续使用缓存结果
管理费率_变动费率1 = v_fee_rate_cache(O_CODE || '_F2_1') / 100
```

### 优化4：MERGE语句替代
**原代码：** 多个UPDATE+子查询
```sql
UPDATE GGZB.TBB_DXSRJS_DM A SET A.标识 = ... WHERE ...;
UPDATE GGZB.TBB_DXSRJS_DM A SET A.标识 = ... WHERE ...;
```

**优化后：** 单个MERGE语句
```sql
MERGE INTO GGZB.TBB_DXSRJS_DM A
USING (SELECT ...所有需要更新的数据...) B
ON (A.ROWID = B.rid)
WHEN MATCHED THEN UPDATE
SET A.标识 = COALESCE(B.new_标识2, B.new_标识);
```

## 性能提升预期

| 优化项目 | 原执行时间 | 优化后时间 | 提升比例 |
|---------|-----------|-----------|---------|
| UPDATE语句 | 高（30+次） | 低（批量） | 70-80% |
| 函数调用 | 高（重复） | 低（缓存） | 60-70% |
| 索引扫描 | 全表扫描 | 索引扫描 | 90%+ |
| 远程查询 | 网络延迟 | 物化视图 | 50-60% |

## 实施建议

1. **首先创建索引**：在非高峰时段创建必要索引
2. **分批实施**：先实施索引和批量UPDATE优化
3. **测试验证**：每个优化步骤后测试性能提升
4. **监控调整**：生产环境监控并进一步调优

## 风险控制

- 索引创建可能暂时影响写性能
- 批量处理需要足够PGA内存
- 缓存机制需要测试内存使用情况
- 建议在测试环境充分验证后再上线
