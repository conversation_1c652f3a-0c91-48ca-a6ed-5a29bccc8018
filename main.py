import pandas as pd
import oracledb
from sqlalchemy import create_engine, text
from datetime import datetime, timedelta
import numpy as np

# --- Configuration ---
# Initialize the Oracle client (only needs to be done once)
# Update this path to your Oracle Instant Client location
try:
    oracledb.init_oracle_client(lib_dir=r"D:\Program\instantclient_19_26")
except Exception as e:
    print(f"Oracle Client already initialized or failed to initialize: {e}")


# Your database connection URL
DB_URL = "oracle+oracledb://ggzb:ggzb@10.35.161.14:8080/dcdata"
TABLE_NAME = "HF_DIGITAL_INTEL_DATA_DS"
SCHEMA_NAME = "GGZB"
UNIQUE_IDENTIFIERS = ['ID']
VALUE_COLUMN = 'DATA_VALUE' # Assuming the value column is named this, please adjust if necessary

# --- Database Context Manager ---
class DatabaseContext:
    """
    Manages the database connection lifecycle using a 'with' statement,
    ensuring resources are properly closed.
    """
    def __init__(self, db_url):
        self.db_url = db_url
        self.engine = None
        self.connection = None

    def __enter__(self):
        try:
            self.engine = create_engine(self.db_url)
            self.connection = self.engine.connect()
            print("Database connection established.")
            return self.connection
        except Exception as e:
            print(f"Database connection failed: {e}")
            return None

    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.connection:
            self.connection.close()
            print("Database connection closed.")
        if self.engine:
            self.engine.dispose()
            print("Database engine disposed.")

# --- Data Fetching Functions ---
def get_partition_data(conn, target_date):
    """
    Step 1 & 2: Fetches data from a specific daily partition.
    """
    date_str = target_date.strftime("%m%d")
    partition_name = f"P_INITIAL_{date_str}"
    
    # In Oracle, table, schema, and partition names are typically uppercase unless quoted
    query = f'SELECT * FROM {SCHEMA_NAME}."{TABLE_NAME}" PARTITION ("{partition_name}")'
    
    try:
        df = pd.read_sql(query, conn)
        # Standardize column names to uppercase for consistency
        df.columns = [col.upper() for col in df.columns]
        print(f"Successfully fetched {df.shape[0]} rows from partition {partition_name}.")
        return df
    except Exception as e:
        print(f"Failed to fetch data from partition {partition_name}: {e}")
        return pd.DataFrame()

def get_historical_data(conn, metrics_to_check, days=7):
    """
    Step 4: Fetches the last 7 days of data ONLY for the metrics that have changed.
    """
    if metrics_to_check.empty:
        return pd.DataFrame()

    print(f"Fetching 7-day history for {len(metrics_to_check)} changed metrics...")
    
    # Generate the list of partition names for the last 7 days
    date_partitions = []
    for i in range(2, days + 2): # Start from 2 days ago up to 8 days ago
        target_date = datetime.now() - timedelta(days=i)
        date_str = target_date.strftime("%m%d")
        partition_name = f"P_INITIAL_{date_str}"
        date_partitions.append(partition_name)
    
    # Create a filter condition for the specific metrics
    # Example: (DEPARTMENT_NAME = 'DeptA' AND METRIC_NAME = 'Metric1') OR (DEPARTMENT_NAME = 'DeptB' AND METRIC_NAME = 'Metric2')
    conditions = []
    for index, row in metrics_to_check.iterrows():
        dept = row[UNIQUE_IDENTIFIERS[0]]
        metric = row[UNIQUE_IDENTIFIERS[1]]
        conditions.append(f'("{UNIQUE_IDENTIFIERS[0]}" = \'{dept}\' AND "{UNIQUE_IDENTIFIERS[1]}" = \'{metric}\')')
    
    metric_filter = " OR ".join(conditions)

    # Build a single query to get all historical data at once
    union_queries = []
    for part in date_partitions:
        union_queries.append(f'SELECT * FROM {SCHEMA_NAME}."{TABLE_NAME}" PARTITION ("{part}") WHERE {metric_filter}')

    full_query = " UNION ALL ".join(union_queries)

    try:
        df_hist = pd.read_sql(text(full_query), conn)
        df_hist.columns = [col.upper() for col in df_hist.columns]
        print(f"Successfully fetched {df_hist.shape[0]} historical rows.")
        return df_hist
    except Exception as e:
        print(f"Failed to fetch historical data: {e}")
        return pd.DataFrame()

# --- Analysis & Reporting Functions ---
def analyze_differences(df_today, df_yesterday):
    """
    Step 3: Compares today's and yesterday's data to find differences.
    """
    # Assuming VALUE_COLUMN holds the numeric value to compare
    value_col_today = f'{VALUE_COLUMN}_TODAY'
    value_col_yesterday = f'{VALUE_COLUMN}_YESTERDAY'

    # Merge dataframes on the unique identifiers
    df_merged = pd.merge(
        df_today, df_yesterday,
        on=UNIQUE_IDENTIFIERS,
        how='outer',
        suffixes=('_TODAY', '_YESTERDAY')
    )

    # Fill NaN for metrics that are new or disappeared
    df_merged[value_col_today] = df_merged[value_col_today].fillna(0)
    df_merged[value_col_yesterday] = df_merged[value_col_yesterday].fillna(0)

    # Find rows where the value has changed
    df_diff = df_merged[df_merged[value_col_today] != df_merged[value_col_yesterday]].copy()

    if df_diff.empty:
        return pd.DataFrame()

    # Calculate percentage change, handling division by zero
    df_diff['CHANGE'] = df_diff[value_col_today] - df_diff[value_col_yesterday]
    df_diff['PERCENTAGE_CHANGE'] = np.where(
        df_diff[value_col_yesterday] != 0,
        (df_diff['CHANGE'] / df_diff[value_col_yesterday]) * 100,
        np.inf # Assign infinity for changes from 0 to a positive number
    )

    # Create an importance score (absolute percentage change)
    df_diff['IMPORTANCE_SCORE'] = df_diff['PERCENTAGE_CHANGE'].abs()
    
    return df_diff.sort_values(by='IMPORTANCE_SCORE', ascending=False)


def generate_report_text(diff_row, historical_df):
    """
    Step 5: Forms the dynamic report narrative for a single metric.
    """
    dept = diff_row[UNIQUE_IDENTIFIERS[0]]
    metric = diff_row[UNIQUE_IDENTIFIERS[1]]
    val_today = diff_row[f'{VALUE_COLUMN}_TODAY']
    val_yesterday = diff_row[f'{VALUE_COLUMN}_YESTERDAY']
    pct_change = diff_row['PERCENTAGE_CHANGE']

    # Analyze historical data for this specific metric
    metric_hist = historical_df[
        (historical_df[UNIQUE_IDENTIFIERS[0]] == dept) &
        (historical_df[UNIQUE_IDENTIFIERS[1]] == metric)
    ]

    hist_narrative = ""
    if not metric_hist.empty:
        hist_avg = metric_hist[VALUE_COLUMN].mean()
        hist_std = metric_hist[VALUE_COLUMN].std()
        
        hist_narrative = f"This compares to a 7-day average of {hist_avg:,.2f}. "
        
        # Check if today's value is an outlier
        if hist_std > 0 and abs(val_today - hist_avg) > 2 * hist_std:
             hist_narrative += "Today's value represents a significant deviation from the recent norm."
        else:
             hist_narrative += "The change appears to be within recent volatility levels."

    # Determine the narrative based on the change
    if pct_change == np.inf:
        verb = "is newly appeared"
        details = f"from 0 to {val_today:,.2f}"
    elif val_today == 0:
        verb = "has disappeared"
        details = f"from {val_yesterday:,.2f} to 0"
    else:
        if pct_change > 50:
            verb = "surged by"
        elif pct_change > 0:
            verb = "increased by"
        elif pct_change < -50:
            verb = "plummeted by"
        else:
            verb = "decreased by"
        details = f"{abs(pct_change):.2f}%, from {val_yesterday:,.2f} to {val_today:,.2f}"

    report_line = f"The '{metric}' metric for the '{dept}' department {verb} {details}. {hist_narrative}"
    return report_line


# --- Main Execution Block ---
if __name__ == "__main__":
    with DatabaseContext(DB_URL) as connection:
        if not connection:
            print("Analysis terminated due to connection failure.")
        else:
            # Step 1 & 2: Get today's and yesterday's data
            today = datetime.now()
            yesterday = today - timedelta(days=1)
            
            df_today = get_partition_data(connection, today)
            df_yesterday = get_partition_data(connection, yesterday)
            
            if df_today.empty or df_yesterday.empty:
                print("Could not retrieve data for today or yesterday. Analysis cannot proceed.")
            else:
                # Step 3: Find differences
                diff_report = analyze_differences(df_today, df_yesterday)
                
                if diff_report.empty:
                    final_report = "Daily Data Dynamics Insight Report\n\n- No significant data changes were detected compared to yesterday."
                else:
                    # Step 4: Get historical data for the top changes
                    top_changes = diff_report.head(5) # Analyze top 5 changes to select best 3
                    historical_data = get_historical_data(connection, top_changes)

                    # Step 5 & 6: Generate final report text for the top 3
                    final_report = "Daily Data Dynamics Insight Report\n"
                    report_items = []
                    for i, row in top_changes.iterrows():
                        report_line = generate_report_text(row, historical_data)
                        report_items.append(report_line)
                        if len(report_items) >= 3:
                            break # Limit to max 3 outputs
                    
                    for i, item in enumerate(report_items, 1):
                        final_report += f"\n{i}. {item}"

                print("\n" + "="*50)
                print(final_report)
                print("="*50)